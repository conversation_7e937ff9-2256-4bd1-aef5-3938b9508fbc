    package com.userprofile.user_profile_service.entity;

    import lombok.AllArgsConstructor;
    import lombok.Data;
    import lombok.NoArgsConstructor;
    import jakarta.persistence.*;
    import java.time.LocalDate;

    @Entity
    @Table(name = "customer_details")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class CustomerDetails {

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Column(name = "user_id", unique = true)
        private String userId;

        // Personal Information
        @Column(name = "first_name")
        private String firstName;

        @Column(name = "last_name")
        private String lastName;

        @Column(name = "full_name", nullable = false)
        private String fullName;

        // Method removed and combined with updateFullName()

        @Column(name = "email_address")
        private String emailAddress;

        @Column(name = "phone_number")
        private String phoneNumber;

        @Column(name = "date_of_birth")
        private LocalDate dateOfBirth;

        @Column(name = "profile_picture")
        private String profilePicture;

        // Primary Address
        @Column(name = "primary_street_address")
        private String primaryStreetAddress;

        @Column(name = "primary_city")
        private String primaryCity;

        @Column(name = "primary_state")
        private String primaryState;

        @Column(name = "primary_zip_code")
        private String primaryZipCode;

        @Column(name = "country")
        private String country;

        // Secondary Address
        @Column(name = "secondary_street_address")
        private String secondaryStreetAddress;

        @Column(name = "secondary_city")
        private String secondaryCity;

        @Column(name = "secondary_state")
        private String secondaryState;

        @Column(name = "secondary_zip_code")
        private String secondaryZipCode;

        // @Column(name = "secondary_country")
        // private String secondaryCountry;

        // Business Details
        @Column(name = "designation_or_role")
        private String designationOrRole;

        @Column(name = "industry_or_domain")
        private String industryOrDomain;

        @Column(name = "company_name")
        private String companyName;

        @Column(name = "preferred_vendor_type")
        private String preferredVendorType;

        @Column(name = "subcategories_of_interest")
        private String subcategoriesOfInterest;

        @Column(name = "categories_of_services_needed")
        private String categoriesOfServicesNeeded;

        // Preferences
        @Column(name = "receive_notifications_from_vendors")
        private boolean receiveNotificationsFromVendors;

        @Column(name = "visible_to_vendors")
        private boolean visibleToVendors;

        @Column(name = "profile_complete")
        private boolean profileComplete;

        /**
         * Lifecycle method to update fullName based on firstName and lastName.
         * Called before entity is persisted or updated.
         */
        @PrePersist
        @PreUpdate
        public void updateFullName() {
            // Ensure fullName is always set based on firstName and lastName
            if (firstName != null || lastName != null) {
                this.fullName = (firstName != null ? firstName : "") + " " +
                        (lastName != null ? lastName : "");
                this.fullName = this.fullName.trim();

                // If fullName is empty after trimming, set a default value
                if (this.fullName.isEmpty()) {
                    this.fullName = "Customer";
                }
            }
        }
    }
