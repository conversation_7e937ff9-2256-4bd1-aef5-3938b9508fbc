import React, { useState, useEffect } from 'react';
import { Layout, theme, Tabs, Spin, Alert } from 'antd';
import { useSelector } from 'react-redux';
import axios from 'axios';
import CustomSider from '../components/Pages/CustomSider';
import ProfileManagement from '../components/Profile/ProfileManagement';
import ProfileCard from '../components/Profile/ProfileCard';
import PageHeader from '../components/Common/PageHeader';
import { UserOutlined, SettingOutlined, EditOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Header, Content, Footer } = Layout;

const Profile = () => {
  const { t } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const user = useSelector((state) => state.auth.user);
 
  const [profileData, setProfileData] = useState(null);
  const [isProfileComplete, setIsProfileComplete] = useState(false);
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);

  const accessRoles = user?.realm_access?.roles || [];
  const roles = ['vendor', 'customer', 'freelancer'];
  const userRoles = roles.filter(role => accessRoles.includes(role));
  const isCustomer = userRoles.includes('customer');
  const isVendor = userRoles.includes('vendor');

  useEffect(() => {
    const fetchProfileData = async () => {
      if (!user?.sub) return;

      console.log("Fetching profile data for user:", user.sub);
      setLoading(true);
      try {
        // Determine which endpoint to use based on user role
        const profileEndpoint = isCustomer
          ? `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/customer/${user.sub}`
          : `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/vendor/${user.sub}`;

        const response = await axios.get(profileEndpoint);
        console.log("Profile data fetched:", response.data);

        setProfileData(response.data);
        setIsProfileComplete(response.data?.profileComplete || false);
      } catch (error) {
        console.error('Error fetching profile data:', error);
        // If we get a 404, it means the profile doesn't exist yet
        if (error.response && error.response.status === 404) {
          setIsProfileComplete(false);
          setProfileData(null);
        } else {
          setError(t('Failed to load profile data. Please try again later.'));
        }
      } finally {
        setLoading(false);
      }
    };

    // Always try to fetch profile data if user exists
    // This ensures we have data available for both view and edit modes
    if (user?.sub) {
      fetchProfileData();
    } else {
      setLoading(false);
    }
  }, [user?.sub, isCustomer, isVendor, t]);

  const handleEditProfile = () => {
    console.log("Edit profile clicked, current profileData:", profileData);
    console.log("Raw profileData dateOfBirth:", profileData?.dateOfBirth);
    setEditMode(true);
  };

  const handleProfileSaved = (savedProfile) => {
    setProfileData(savedProfile);
    setIsProfileComplete(true);
    setEditMode(false);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <CustomSider collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout style={{
        padding: '0',
        width: '100%',
        maxWidth: 'auto',
        margin: '0 auto',
        marginLeft: collapsed ? '80px' : '220px',
        transition: 'margin-left 0.3s'
      }}>


            <PageHeader
              title={t('Profile Management')}
              timestamp="Last login: April 12, 2023 - 5:07 PM"
              showSearch={true}
              onSearch={(value) => console.log('Search:', value)}
              userAvatar="https://xsgames.co/randomusers/avatar.php?g=pixel"
            />

        <Content style={{ margin: '0 16px' }}>
          <div
            style={{
              minHeight: 360,
              background: colorBgContainer,
              borderRadius: borderRadiusLG,
            }}
          >
            {loading ? (
              <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
                <Spin size="large" tip={t('Loading profile data...')} />
              </div>
            ) : error ? (
              <Alert
                message={t('Error')}
                description={error}
                type="error"
                showIcon
                style={{ margin: '20px' }}
              />
            ) : !isProfileComplete || editMode ? (
              <Tabs defaultActiveKey="profile-setup" size="large" style={{ padding: '20px' }}>
                <Tabs.TabPane
                  tab={<span><UserOutlined />{t('Profile Setup')}</span>}
                  key="profile-setup"
                >
                
                  <ProfileManagement
                    initialData={profileData}
                    editMode={editMode}
                    onProfileSaved={handleProfileSaved}
                  />
                </Tabs.TabPane>
              </Tabs>
            ) : (
              <div style={{ padding: '20px' }}>
                <ProfileCard
                  profileData={profileData}
                  isCustomer={isCustomer}
                  onEdit={handleEditProfile}
                />
              </div>
            )}
          </div>
        </Content>
        <Footer style={{ textAlign: 'center' }}>
          {/* Ant Design ©{new Date().getFullYear()} Created by Ant UED */}
        </Footer>
      </Layout>
    </Layout>
  );
};

export default Profile;