import React, { useState, useEffect } from 'react';
import { Modal, Button, Spin, Alert, Space, Typography, message } from 'antd';
import {
  DownloadOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  FileZipOutlined,
  FileUnknownOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './FilePreviewModal.css';
import axios from 'axios';

const { Text, Link } = Typography;

/**
 * FilePreviewModal component for previewing files before downloading
 * @param {Object} props Component props
 * @param {boolean} props.visible Whether the modal is visible
 * @param {function} props.onClose Callback when the modal is closed
 * @param {string} props.fileUrl URL of the file to preview
 * @param {string} props.downloadUrl URL for downloading the file
 * @param {string} props.fileName Name of the file
 */
const FilePreviewModal = ({ visible, onClose, fileUrl, downloadUrl, fileName }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [fileType, setFileType] = useState('unknown');

  // Log props when they change - simplified since we're using the working URL


  // Determine file type from extension
  useEffect(() => {
    if (!fileName) return;

    const extension = fileName.split('.').pop().toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
      setFileType('image');
    } else if (extension === 'pdf') {
      setFileType('pdf');
    } else if (['doc', 'docx', 'rtf', 'odt'].includes(extension)) {
      setFileType('word');
    } else if (['xls', 'xlsx', 'csv', 'ods'].includes(extension)) {
      setFileType('excel');
    } else if (['txt', 'text', 'json', 'xml', 'html', 'htm', 'md', 'log'].includes(extension)) {
      setFileType('text');
    } else if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(extension)) {
      setFileType('zip');
    } else {
      setFileType('unknown');
    }

    // Reset error state when file type changes
    setError(null);
  }, [fileName]);

  // Reset loading state when modal becomes visible - simplified
  useEffect(() => {
    if (visible && fileUrl) {
      console.log('FilePreviewModal: Modal opened with fileUrl:', fileUrl, 'fileType:', fileType);
      setLoading(true);
      setError(null);

      // For images and PDFs, we'll let the onLoad/onError handlers manage loading state
      if (fileType !== 'image' && fileType !== 'pdf') {
        // For other file types, we'll simulate a loading delay
        const timer = setTimeout(() => {
          setLoading(false);
        }, 1000);

        return () => clearTimeout(timer);
      }
    } else if (!visible) {
      // Reset state when modal is closed
      setLoading(true);
      setError(null);
    }
  }, [visible, fileUrl, fileType]);

  // Get appropriate icon based on file type
  const getFileIcon = () => {
    switch (fileType) {
      case 'image':
        return <FileImageOutlined />;

      case 'pdf':
        return <FilePdfOutlined />;
      case 'word':
        return <FileWordOutlined />;
      case 'excel':
        return <FileExcelOutlined />;
      case 'text':
        return <FileTextOutlined />;
      case 'zip':
        return <FileZipOutlined />;
      case 'unknown':
        return <FileUnknownOutlined />;
      default:
        return <FileOutlined />;
    }
  };

  // Handle file load events - simplified since we're using the working URL
  const handleFileLoad = () => {
    console.log('FilePreviewModal: File loaded successfully for URL:', fileUrl);
    setLoading(false);
    setError(null);
  };

  const handleFileError = (error) => {
    console.error('FilePreviewModal: Error loading file:', fileUrl);
    console.error('FilePreviewModal: Error event details:', error.target ? {
      src: error.target.src,
      naturalWidth: error.target.naturalWidth,
      naturalHeight: error.target.naturalHeight,
      complete: error.target.complete
    } : 'No target available');

    setLoading(false);
    setError(t('Failed to load file. Please try downloading instead.'));
  };

  // Render file content based on type
  const renderFilePreview = () => {
    if (loading) {
      return (
        <div className="file-preview-loading">
          <Spin size="large" />
          <div className="loading-text">{t('Loading preview...')}</div>
        </div>
      );
    }

    if (error) {
      return (
        <Alert
          message={t('Preview Error')}
          description={error}
          type="error"
          showIcon
          action={
            <Button
              type="primary"
              onClick={() => {
                // Create a temporary anchor element to download with credentials
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = fileName;
                a.rel = 'noopener noreferrer';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
              }}
            >
              {t('Download Instead')}
            </Button>
          }
        />
      );
    }

    switch (fileType) {
      case 'image':
        return (
          <div className="image-preview-container">
            {loading && (
              <div className="file-preview-loading">
                <Spin size="large" />
                <div className="loading-text">{t('Loading image...')}</div>
              </div>
            )}
            <img
              src={fileUrl}
              alt={fileName}
              className="preview-image"
              style={{
                display: loading ? 'none' : 'block',
                maxWidth: '100%',
                maxHeight: '100%'
              }}
              onLoad={(e) => {
                console.log('FilePreviewModal: Image onLoad fired for:', fileUrl);
                console.log('FilePreviewModal: Image dimensions:', {
                  naturalWidth: e.target.naturalWidth,
                  naturalHeight: e.target.naturalHeight,
                  complete: e.target.complete
                });
                handleFileLoad();
              }}
              onError={(e) => {
                console.error('FilePreviewModal: Image onError fired for:', fileUrl);
                handleFileError(e);
              }}
            />
          </div>
        );
      case 'pdf':
        return (
          <div className="pdf-preview-container">
            {loading && (
              <div className="file-preview-loading">
                <Spin size="large" />
                <div className="loading-text">{t('Loading PDF...')}</div>
              </div>
            )}
            <iframe
              src={fileUrl}
              title={fileName}
              className="preview-iframe"
              style={{ display: loading ? 'none' : 'block' }}
              onLoad={handleFileLoad}
              onError={(e) => handleFileError(e)}
              sandbox="allow-scripts allow-same-origin"
              referrerPolicy="origin"
            />
          </div>
        );
      case 'text':
        // For text files, we'll try to display them in an iframe
        return (
          <div className="text-preview-container">
            {loading && (
              <div className="file-preview-loading">
                <Spin size="large" />
                <div className="loading-text">{t('Loading text...')}</div>
              </div>
            )}
            <iframe
              src={fileUrl}
              title={fileName}
              className="preview-iframe"
              style={{ display: loading ? 'none' : 'block' }}
              onLoad={handleFileLoad}
              onError={(e) => handleFileError(e)}
              sandbox="allow-scripts allow-same-origin"
              referrerPolicy="origin"
            />
          </div>
        );
      case 'word':
      case 'excel':
        // For Office documents, we'll provide a direct link to open in browser
        return (
          <div className="office-preview-container">
            <Space direction="vertical" size="large" align="center">
              {getFileIcon()}
              <Text>{t('Office documents may not preview correctly in all browsers')}</Text>
              <Button
                type="primary"
                // icon={<EyeOutlined />}
                onClick={() => window.open(fileUrl, '_blank', 'noopener,noreferrer')}
              >
                {t('Open in Browser')}
              </Button>
              <Text type="secondary">{t('Or use the download button below to open in your preferred application')}</Text>
            </Space>
          </div>
        );
      case 'zip':
        // For archive files, we'll just show a download option
        return (
          <div className="archive-preview-container">
            <Space direction="vertical" size="large" align="center">
              {getFileIcon()}
              <Text>{t('Archive files cannot be previewed directly')}</Text>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={() => {
                  // Create a temporary anchor element to download with credentials
                  const a = document.createElement('a');
                  a.href = downloadUrl;
                  a.download = fileName;
                  a.rel = 'noopener noreferrer';
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                }}
              >
                {t('Download File')}
              </Button>
            </Space>
          </div>
        );
      default:
        return (
          <div className="generic-preview-container">
            <Space direction="vertical" size="large" align="center">
              {getFileIcon()}
              <Text>{t('This file type cannot be previewed directly')}</Text>
              <Text>{t('Click the download button to view this file')}</Text>
              <Button
                type="primary"
                icon={<EyeOutlined />}
                onClick={() => window.open(fileUrl, '_blank', 'noopener,noreferrer')}
              >
                {t('Try to Open in Browser')}
              </Button>
            </Space>
          </div>
        );
    }
  };

  return (
    <Modal
      title={
        <Space>
          {getFileIcon()}
          <span>{fileName || t('File Preview')}</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width="80%"
      centered
      className="file-preview-modal"
      footer={[
        <Button key="close" onClick={onClose}>
          {t('Close')}
        </Button>,
        <Button
          key="download"
          type="primary"
          icon={<DownloadOutlined />}
          onClick={() => {
            // Create a temporary anchor element to download with credentials
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = fileName;
            a.rel = 'noopener noreferrer';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }}
        >
          {t('Download')}
        </Button>,
      ]}
    >
      <div className="file-preview-content">
        {renderFilePreview()}
      </div>
    </Modal>
  );
};

export default FilePreviewModal;
