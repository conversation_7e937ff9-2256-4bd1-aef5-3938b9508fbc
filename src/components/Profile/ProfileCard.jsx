import React, { useState, useEffect } from 'react';
import { Card, Avatar, Descriptions, Button, Divider, Typography, Row, Col, Tag, Tooltip, Spin } from 'antd';
import {
  EditOutlined,
  UserOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
  BankOutlined,
  CalendarOutlined,
  TeamOutlined,
  GlobalOutlined,
  ShopOutlined,
  DownloadOutlined,
  EyeOutlined,
  FileOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './ProfileCard.css';
import FilePreviewModal from '../Common/FilePreviewModal';
import DocumentsList from '../Common/DocumentsList';

const { Title, Text } = Typography;

// Helper function to get download URL for file fields
const getFileUrl = (filename) => {
  if (!filename) return null;
  if (filename.startsWith('http')) return filename;
  return `${import.meta.env.VITE_APP_API_BASE_URL_UPLOAD}/download/${filename}`;
};

// Helper function to get view URL for file fields
const getFileViewUrl = (filename) => {
  if (!filename) return null;
  if (filename.startsWith('http')) return filename;
  return `${import.meta.env.VITE_APP_API_BASE_URL_UPLOAD}/view/${filename}`;
};

const ProfileCard = ({ profileData, isCustomer, onEdit }) => {
  const { t } = useTranslation();
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState({
    url: '',
    downloadUrl: '',
    name: ''
  });

  // Function to open the file preview modal
  const handlePreviewFile = (filename, displayName) => {
    if (!filename) return;

    console.log('Opening file preview for:', filename);

    // Get the file extension to display in the modal
    const fileExtension = filename.includes('.') ?
      filename.substring(filename.lastIndexOf('.')) : '';

    const displayFileName = displayName ||
      (filename.includes('_') ? filename.split('_').pop() : filename);

    // Combine display name with extension if needed
    const fullDisplayName = displayFileName.includes('.') ?
      displayFileName : displayFileName + fileExtension;

    const viewUrl = getFileViewUrl(filename);
    const downloadUrl = getFileUrl(filename);

    // console.log('View URL:', viewUrl);
    // console.log('Download URL:', downloadUrl);
    // console.log('API Base URL:', import.meta.env.VITE_APP_API_BASE_URL_UPLOAD);

    // Test if the URL is accessible
    fetch(viewUrl, {
      method: 'HEAD',
      credentials: 'include',  // Include credentials (cookies) in the request
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })
      .then(response => {
        console.log('File view URL response:', response.status, response.statusText);
      })
      .catch(error => {
        console.error('Error checking file view URL:', error);
      });

    setPreviewFile({
      url: viewUrl,
      downloadUrl: downloadUrl,
      name: fullDisplayName
    });
    setPreviewVisible(true);
  };

  if (!profileData) {
    return (
      <Card loading={true} style={{ width: '100%', marginBottom: 20 }}>
        <p>Loading profile data...</p>
      </Card>
    );

  }

  console.log('Profile data in ProfileCard:', profileData);
  console.log(getFileUrl(profileData.profilePicture));

  const renderCustomerProfile = () => (
    <div className="profile-card-container">
      <Card className="profile-card" bordered={false}>
        <div className="profile-header">
          <div className="profile-avatar">
            {profileData.profilePicture ? (
              <Tooltip title={t('Click to preview image')}>
                <div
                  className="avatar-link"
                  onClick={() => handlePreviewFile(profileData.profilePicture, t('Profile Picture'))}
                >
                  <div className="avatar-container">
                    <Avatar
                      size={120}
                      src={getFileUrl(profileData.profilePicture)}
                      className="clickable-avatar"
                    />
                    <div className="avatar-overlay">
                      <span className="view-text">{t('Preview')}</span>
                    </div>
                  </div>
                </div>
              </Tooltip>
            ) : (
              <Tooltip title={t('No profile picture')}>
                <Avatar
                  size={120}
                  icon={<UserOutlined />}
                />
              </Tooltip>
            )}
          </div>
          <div className="profile-title">
            <Title level={3}>{profileData.fullName || `${profileData.firstName} ${profileData.lastName}`}</Title>
            <Text type="secondary">{profileData.designationOrRole || 'Customer'}</Text>
            <div className="profile-tags">
              <Tag color="blue">Customer</Tag>
              {profileData.industryOrDomain && <Tag color="purple">{profileData.industryOrDomain}</Tag>}
              {profileData.companyName && <Tag color="cyan">{profileData.companyName}</Tag>}
            </div>
          </div>
          <div className="profile-actions">
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={onEdit}
              size="large"
            >
              {t('Edit Profile')}
            </Button>
          </div>
        </div>

        <Divider style={{ margin: '0 0 24px 0' }} />

        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Card
              title={t('Personal Information')}
              className="profile-section-card"
              bordered={false}
              extra={<UserOutlined />}
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label={t('Full Name')}>
                  <UserOutlined style={{ marginRight: 8 }} />
                  {profileData.fullName || `${profileData.firstName} ${profileData.lastName}`}
                </Descriptions.Item>
                <Descriptions.Item label={t('Email')}>
                  <MailOutlined style={{ marginRight: 8 }} />
                  {profileData.emailAddress}
                </Descriptions.Item>
                <Descriptions.Item label={t('Phone')}>
                  <PhoneOutlined style={{ marginRight: 8 }} />
                  {profileData.phoneNumber || t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Date of Birth')}>
                  <CalendarOutlined style={{ marginRight: 8 }} />
                  {profileData.dateOfBirth || t('Not provided')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} md={12}>
            <Card
              title={t('Address Information')}
              className="profile-section-card"
              bordered={false}
              extra={<EnvironmentOutlined />}
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label={t('Street Address')}>
                  <EnvironmentOutlined style={{ marginRight: 8 }} />
                  {profileData.primaryStreetAddress}
                </Descriptions.Item>
                <Descriptions.Item label={t('City')}>
                  {profileData.primaryCity}
                </Descriptions.Item>
                <Descriptions.Item label={t('State')}>
                  {profileData.primaryState}
                </Descriptions.Item>
                <Descriptions.Item label={t('Zip Code')}>
                  {profileData.primaryZipCode}
                </Descriptions.Item>
                <Descriptions.Item label={t('Country')}>
                  <GlobalOutlined style={{ marginRight: 8 }} />
                  {profileData.country}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: '24px', marginBottom: '12px' }}>
          <Col xs={24} md={12}>
            <Card
              title={t('Business Information')}
              className="profile-section-card"
              bordered={false}
              extra={<BankOutlined />}
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label={t('Company')}>
                  <BankOutlined style={{ marginRight: 8 }} />
                  {profileData.companyName || t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Industry')}>
                  <ShopOutlined style={{ marginRight: 8 }} />
                  {profileData.industryOrDomain || t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Role')}>
                  <TeamOutlined style={{ marginRight: 8 }} />
                  {profileData.designationOrRole || t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Preferred Vendor Type')}>
                  {profileData.preferredVendorType || t('Not provided')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} md={12}>
            <Card
              title={t('Preferences')}
              className="profile-section-card"
              bordered={false}
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label={t('Receive Notifications')}>
                  {profileData.receiveNotificationsFromVendors ?
                    <Tag color="green">{t('Yes')}</Tag> :
                    <Tag color="red">{t('No')}</Tag>}
                </Descriptions.Item>
                <Descriptions.Item label={t('Visible to Vendors')}>
                  {profileData.visibleToVendors ?
                    <Tag color="green">{t('Yes')}</Tag> :
                    <Tag color="red">{t('No')}</Tag>}
                </Descriptions.Item>
                <Descriptions.Item label={t('Categories of Interest')}>
                  {profileData.subcategoriesOfInterest ?
                    profileData.subcategoriesOfInterest.split(',').map(cat => (
                      <Tag color="blue" key={cat.trim()}>{cat.trim()}</Tag>
                    )) :
                    t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Services Needed')}>
                  {profileData.categoriesOfServicesNeeded ?
                    profileData.categoriesOfServicesNeeded.split(',').map(service => (
                      <Tag color="purple" key={service.trim()}>{service.trim()}</Tag>
                    )) :
                    t('Not provided')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );

  const renderVendorProfile = () => (
    <div className="profile-card-container">
      <Card className="profile-card" bordered={false}>
        <div className="profile-header">
          <div className="profile-avatar">
            {profileData.businessLogo ? (
              <Tooltip title={t('Click to preview image')}>
                <div
                  className="avatar-link"
                  onClick={() => handlePreviewFile(profileData.businessLogo, t('Business Logo'))}
                >
                  <div className="avatar-container">
                    <Avatar
                      size={120}
                      src={getFileUrl(profileData.businessLogo)}
                      alt={profileData.businessName}
                      className="clickable-avatar"
                    />
                    <div className="avatar-overlay">
                      <span className="view-text">{t('Preview')}</span>
                    </div>
                  </div>
                </div>
              </Tooltip>
            ) : (
              <Tooltip title={t('No business logo')}>
                <Avatar size={120} icon={<ShopOutlined />} />
              </Tooltip>
            )}
          </div>
          <div className="profile-title">
            <Title level={3}>{profileData.businessName}</Title>
            <Text type="secondary">{profileData.businessCategory}</Text>
            <div className="profile-tags">
              <Tag color="green">Vendor</Tag>
              {profileData.businessSubcategory && <Tag color="orange">{profileData.businessSubcategory}</Tag>}
              {profileData.yearsOfExperience && <Tag color="geekblue">{t('Experience')}: {profileData.yearsOfExperience} {t('years')}</Tag>}
            </div>
          </div>
          <div className="profile-actions">
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={onEdit}
              size="large"
            >
              {t('Edit Profile')}
            </Button>
          </div>
        </div>

        <Divider style={{ margin: '0 0 24px 0' }} />

        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Card
              title={t('Business Information')}
              className="profile-section-card"
              bordered={false}
              extra={<BankOutlined />}
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label={t('Business Name')}>
                  <BankOutlined style={{ marginRight: 8 }} />
                  {profileData.businessName}
                </Descriptions.Item>
                <Descriptions.Item label={t('Email')}>
                  <MailOutlined style={{ marginRight: 8 }} />
                  {profileData.businessEmail}
                </Descriptions.Item>
                <Descriptions.Item label={t('Phone')}>
                  <PhoneOutlined style={{ marginRight: 8 }} />
                  {profileData.businessPhone || t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Website')}>
                  <GlobalOutlined style={{ marginRight: 8 }} />
                  {profileData.businessWebsite ? (
                    <a href={profileData.businessWebsite.startsWith('http') ? profileData.businessWebsite : `https://${profileData.businessWebsite}`} target="_blank" rel="noopener noreferrer">
                      {profileData.businessWebsite}
                    </a>
                  ) : t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Years of Experience')}>
                  <CalendarOutlined style={{ marginRight: 8 }} />
                  {profileData.yearsOfExperience || t('Not provided')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} md={12}>
            <Card
              title={t('Address Information')}
              className="profile-section-card"
              bordered={false}
              extra={<EnvironmentOutlined />}
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label={t('Street Address')}>
                  <EnvironmentOutlined style={{ marginRight: 8 }} />
                  {profileData.streetAddress}
                </Descriptions.Item>
                <Descriptions.Item label={t('City')}>
                  {profileData.city}
                </Descriptions.Item>
                <Descriptions.Item label={t('State')}>
                  {profileData.state}
                </Descriptions.Item>
                <Descriptions.Item label={t('Zip Code')}>
                  {profileData.zipCode}
                </Descriptions.Item>
                <Descriptions.Item label={t('Country')}>
                  <GlobalOutlined style={{ marginRight: 8 }} />
                  {profileData.country}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: '24px', marginBottom: '12px' }}>
          <Col xs={24} md={12}>
            <Card
              title={t('Product Information')}
              className="profile-section-card"
              bordered={false}
              extra={<ShopOutlined />}
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label={t('Product Name')}>
                  <ShopOutlined style={{ marginRight: 8 }} />
                  {profileData.productName || t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Product Category')}>
                  {profileData.productCategory ? (
                    <Tag color="blue">{profileData.productCategory}</Tag>
                  ) : t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Product Description')}>
                  {profileData.productDescription || t('Not provided')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} md={12}>
            <Card
              title={t('Business Details')}
              className="profile-section-card"
              bordered={false}
              extra={<TeamOutlined />}
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label={t('Registration Number')}>
                  {profileData.registrationNumber || t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Regions Supported')}>
                  {profileData.regionsSupported ?
                    profileData.regionsSupported.split(',').map(region => (
                      <Tag color="cyan" key={region.trim()}>{region.trim()}</Tag>
                    )) :
                    t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('License Details')}>
                  {profileData.licenseDetails || t('Not provided')}
                </Descriptions.Item>
                <Descriptions.Item label={t('Certificate')}>
                  {profileData.certificate ? (
                    <div className="certificate-container">
                      <span className="certificate-name">{profileData.certificate.split('_').pop()}</span>
                      <div className="certificate-actions">
                        <Tooltip title={t('Preview Certificate')}>
                          <Button
                            type="link"
                            className="file-link view-link"
                            onClick={() => handlePreviewFile(profileData.certificate, t('Business Certificate'))}
                          >
                            <EyeOutlined />
                          </Button>
                        </Tooltip>
                        <Tooltip title={t('Download Certificate')}>
                          <a
                            href={getFileUrl(profileData.certificate)}
                            download={profileData.certificate.split('_').pop()}
                            className="file-link download-link"
                          >
                            <DownloadOutlined />
                          </a>
                        </Tooltip>
                      </div>
                    </div>
                  ) : t('Not provided')}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
          <Col xs={24}>
            <Card
              title={t('Additional Documents')}
              className="profile-section-card"
              bordered={false}
              extra={<FileOutlined />}
            >
              {profileData.userId && (
                <DocumentsList
                  key={`documents-list-${profileData.userId}`}
                  userId={profileData.userId}
                  readOnly={false}
                  showActions={true}
                />
              )}
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );

  return (
    <>
      {isCustomer ? renderCustomerProfile() : renderVendorProfile()}

      <FilePreviewModal
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
        fileUrl={previewFile.url}
        downloadUrl={previewFile.downloadUrl}
        fileName={previewFile.name}
      />
    </>
  );
};

export default ProfileCard;
