import React, { useEffect } from 'react';
import { Form, Input, DatePicker, Checkbox, message } from 'antd';
import { useTranslation } from 'react-i18next';
import FileUpload from '../Common/FileUpload';
import MultiFileUpload from '../Common/MultiFileUpload';

export const FormFields = ({ inputs, userFields, form, allFormValues }) => {
  const { t } = useTranslation();

  // Set initial values for user fields and existing form values when component mounts
  useEffect(() => {
    if (userFields || allFormValues) {
      const initialValues = {
        // User fields
        firstName: userFields?.first_name || allFormValues?.firstName,
        lastName: userFields?.last_name || allFormValues?.lastName,
        emailAddress: userFields?.email || allFormValues?.emailAddress,
        phoneNumber: userFields?.phone || allFormValues?.phoneNumber,
        // Other form fields
        ...allFormValues
      };
      
      // Remove undefined values
      Object.keys(initialValues).forEach(key => {
        if (initialValues[key] === undefined) {
          delete initialValues[key];
        }
      });

      console.log('Setting form values:', initialValues);
      form.setFieldsValue(initialValues);
    }
  }, [userFields, allFormValues, form]);

  const getFieldName = (label) => {
    const fieldMappings = {
      'First Name': 'firstName',
      'Last Name': 'lastName',
      'Email': 'emailAddress',
      'Phone Number': 'phoneNumber',
      'Date of Birth': 'dateOfBirth',
      'Profile Picture': 'profilePicture',
      'Street Address': 'primaryStreetAddress',
      'City': 'primaryCity',
      'State': 'primaryState',
      'Zip Code': 'primaryZipCode',
      'Country': 'country',
      'Designation/Role': 'designationOrRole',
      'Industry/Domain': 'industryOrDomain',
      'Company Name': 'companyName',
      'Preferred Vendor Type': 'preferredVendorType',
      'Subcategories of Interest': 'subcategoriesOfInterest',
      'Categories of Services Needed': 'categoriesOfServicesNeeded',
      'Receive notifications from vendors': 'receiveNotificationsFromVendors',
      'Visible to Vendors': 'visibleToVendors'
    };

    return fieldMappings[label] || label.toLowerCase().replace(/\s+/g, '_').replace(/\//g, '/');
  };

  const renderFormItems = (inputs) => {
    // Handle both array and object formats
    const fieldsToRender = Array.isArray(inputs) ? inputs : inputs.fields || [];
    
    return fieldsToRender.map((input) => {
      const { id, label, type, disabled } = input;
      const fieldName = getFieldName(label);
      
      const isUserField = ['firstName', 'lastName', 'emailAddress', 'phoneNumber'].includes(fieldName);
      const hasUserData = isUserField && (userFields?.[fieldName] || allFormValues?.[fieldName]);

      switch (type) {
        case 'text':
        case 'email':
        case 'tel':
          return (
            <div key={id} className="form-item-wrapper">
              <Form.Item
                name={fieldName}
                label={
                  <span>
                    {t(label)}
                    {hasUserData && 
                      <span style={{ color: '#8c8c8c', marginLeft: '8px', fontSize: '12px' }}>
                        ({t('Auto-filled')})
                      </span>
                    }
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: t(`Please enter your ${label}`)
                  }
                ]}
                className="form-item"
                initialValue={allFormValues?.[fieldName] || userFields?.[fieldName]}
              >
                <Input
                  placeholder={t(`Enter ${label}`)}
                  disabled={hasUserData || disabled}
                  className={hasUserData ? 'disabled-input' : ''}
                  type={type === 'email' ? 'email' : 'text'}
                />
              </Form.Item>
            </div>
          );
        case 'date':
          return (
            <div key={id} className="form-item-wrapper">
              <Form.Item
                name={fieldName}
                label={t(label)}
                rules={[{ required: true, message: t(`Please select ${label.toLowerCase()}`) }]}
                className="form-item"
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
                initialValue={allFormValues?.[fieldName]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </div>
          );
        case 'file':
          return (
            <div key={id} className="form-item-wrapper">
              <Form.Item
                name={fieldName}
                label={t(label)}
                className="form-item"
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
                extra={input.description ? t(input.description) : null}
                initialValue={allFormValues?.[fieldName]}
              >
                <FileUpload
                  fileType={input.fileType || label.toLowerCase().replace(/\s+/g, '_')}
                  buttonText={t(`Upload ${label}`)}
                  accept={input.accept || '*/*'}
                  maxCount={1}
                  apiUrl={`${import.meta.env.VITE_APP_API_BASE_URL_UPLOAD}/upload`}
                  onUploadSuccess={(data) => {
                    console.log('File upload success data:', data);
                    form.setFieldsValue({ [fieldName]: data.fileName });
                  }}
                  onUploadError={(error) => {
                    message.error(`Upload failed: ${error.message}`);
                  }}
                />
              </Form.Item>
            </div>
          );
        case 'multi-file':
          return (
            <div key={id} className="form-item-wrapper">
              <Form.Item
                name={fieldName}
                label={t(label)}
                className="form-item"
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
                extra={input.description ? t(input.description) : null}
                initialValue={allFormValues?.[fieldName]}
              >
                <MultiFileUpload
                  userId={user?.sub}
                  fileType={input.fileType || label.toLowerCase().replace(/\s+/g, '_')}
                  accept={input.accept || '*/*'}
                  multiple={input.multiple || true}
                  maxCount={input.maxCount || 10}
                  uploadApiUrl={`${import.meta.env.VITE_APP_API_BASE_PROFILE_DOC_URL}/vendor-documents/upload`}
                  listApiUrl={`${import.meta.env.VITE_APP_API_BASE_PROFILE_DOC_URL}/vendor-documents/user`}
                  deleteApiUrl={`${import.meta.env.VITE_APP_API_BASE_PROFILE_DOC_URL}/vendor-documents`}
                  onUploadSuccess={(data) => {
                    console.log('Document upload success:', data);
                    message.success(`${data.originalFileName || data.fileName} uploaded successfully`);
                  }}
                  onUploadError={(error) => {
                    message.error(`Upload failed: ${error.message}`);
                  }}
                  onDeleteSuccess={(data) => {
                    console.log('Document deleted:', data);
                    message.success(`Document deleted successfully`);
                  }}
                />
              </Form.Item>
            </div>
          );
        case 'checkbox':
          return (
            <div key={id} className="form-item-wrapper">
              <Form.Item
                name={fieldName}
                valuePropName="checked"
                className="form-item"
                labelCol={{ span: 0 }}
                wrapperCol={{ span: 24 }}
                initialValue={allFormValues?.[fieldName]}
              >
                <Checkbox>{t(label)}</Checkbox>
              </Form.Item>
            </div>
          );
        default:
          return null;
      }
    });
  };

  return (
    <div className="form-section">
      {!Array.isArray(inputs) && inputs.title && (
        <div className="form-section-title">{t(inputs.title)}</div>
      )}
      <div className="form-row">
        {renderFormItems(inputs)}
      </div>
    </div>
  );
}; 