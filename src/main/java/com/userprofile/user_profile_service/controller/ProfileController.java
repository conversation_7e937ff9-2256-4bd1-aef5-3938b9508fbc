package com.userprofile.user_profile_service.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.userprofile.user_profile_service.entity.CustomerDetails;
import com.userprofile.user_profile_service.entity.VendorDetails;
import com.userprofile.user_profile_service.service.ProfileService;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for user-centric profile operations.
 * This controller focuses on operations that span multiple profile types
 * or provide a unified interface for profile management.
 */
@RestController
@RequestMapping("/api/profiles")
@CrossOrigin(origins = { "http://localhost:5173", "http://localhost:3000", "http://localhost:8081" })
public class ProfileController {

    @Autowired
    private ProfileService profileService;

    /**
     * Check if a profile exists for the given user ID
     *
     * @param userId The user ID to check
     * @return Response with profile existence status
     */
    @GetMapping("/exists/{userId}")
    public ResponseEntity<Map<String, Object>> checkProfileExists(@PathVariable String userId) {
        Map<String, Object> response = new HashMap<>();
        boolean customerExists = profileService.customerProfileExists(userId);
        boolean vendorExists = profileService.vendorProfileExists(userId);

        response.put("customerProfileExists", customerExists);
        response.put("vendorProfileExists", vendorExists);
        response.put("anyProfileExists", customerExists || vendorExists);

        return ResponseEntity.ok(response);
    }

    /**
     * Get customer profile by user ID - Redirects to CustomerController
     * Maintained for backward compatibility
     *
     * @param userId The user ID
     * @return The customer profile if found
     */
    @GetMapping("/customer/{userId}")
    public ResponseEntity<?> getCustomerProfile(@PathVariable String userId) {
        Optional<CustomerDetails> optionalProfile = profileService.getCustomerDetailsByUserId(userId);
        if (optionalProfile.isPresent()) {
            return ResponseEntity.ok(optionalProfile.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get vendor profile by user ID - Redirects to VendorController
     * Maintained for backward compatibility
     *
     * @param userId The user ID
     * @return The vendor profile if found
     */
    @GetMapping("/vendor/{userId}")
    public ResponseEntity<?> getVendorProfile(@PathVariable String userId) {
        Optional<VendorDetails> optionalProfile = profileService.getVendorDetailsByUserId(userId);

        return optionalProfile
                .<ResponseEntity<?>>map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("message", "Vendor profile not found for user ID: " + userId)));
    }

    /**
     * Create or update customer profile - Redirects to CustomerController
     * Maintained for backward compatibility
     *
     * @param customerDetails The customer details to save
     * @return The saved customer details
     */
    @PostMapping("/customer")
    public ResponseEntity<CustomerDetails> saveCustomerProfile(@RequestBody CustomerDetails customerDetails) {
        CustomerDetails savedProfile = profileService.saveCustomerProfile(customerDetails);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedProfile);
    }

    /**
     * Create or update vendor profile - Redirects to VendorController
     * Maintained for backward compatibility
     *
     * @param vendorDetails The vendor details to save
     * @return The saved vendor details
     */
    @PostMapping("/vendor")
    public ResponseEntity<VendorDetails> saveVendorProfile(@RequestBody VendorDetails vendorDetails) {
        VendorDetails savedProfile = profileService.saveVendorProfile(vendorDetails);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedProfile);
    }

    /**
     * Update customer profile by user ID - Maintained for backward compatibility
     *
     * @param userId          The user ID
     * @param customerDetails The customer details to update
     * @return The updated customer details
     */
    @PutMapping("/customer/{userId}")
    public ResponseEntity<?> updateCustomerProfile(@PathVariable String userId,
            @RequestBody CustomerDetails customerDetails) {
        Optional<CustomerDetails> optionalCustomer = profileService.getCustomerDetailsByUserId(userId);

        if (optionalCustomer.isPresent()) {
            CustomerDetails existingCustomer = optionalCustomer.get();
            customerDetails.setId(existingCustomer.getId());
            CustomerDetails updatedCustomer = profileService.saveCustomerProfile(customerDetails);
            return ResponseEntity.ok(updatedCustomer);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Customer profile not found for user ID: " + userId));
        }
    }

    /**
     * Update vendor profile by user ID - Maintained for backward compatibility
     *
     * @param userId        The user ID
     * @param vendorDetails The vendor details to update
     * @return The updated vendor details
     */
    @PutMapping("/vendor/{userId}")
    public ResponseEntity<?> updateVendorProfile(@PathVariable String userId,
            @RequestBody VendorDetails vendorDetails) {
        Optional<VendorDetails> optionalVendor = profileService.getVendorDetailsByUserId(userId);

        if (optionalVendor.isPresent()) {
            VendorDetails existingVendor = optionalVendor.get();
            vendorDetails.setId(existingVendor.getId());
            VendorDetails updatedVendor = profileService.saveVendorProfile(vendorDetails);
            return ResponseEntity.ok(updatedVendor);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Vendor profile not found for user ID: " + userId));
        }
    }
}
