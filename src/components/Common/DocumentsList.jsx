import React, { useState, useEffect } from 'react';
import { List, Button, Spin, Typography, Space, Empty, Tooltip, message, Modal } from 'antd';
import {
  FileOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  FileZipOutlined,
  FileUnknownOutlined,
  EyeOutlined,
  DownloadOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import './DocumentsList.css';
import FilePreviewModal from './FilePreviewModal';

const { Text } = Typography;

/**
 * Component for displaying a list of documents with view, download, and delete options
 * @param {Object} props Component props
 * @param {string} props.userId User ID for fetching documents
 * @param {boolean} props.readOnly If true, hide delete buttons
 * @param {boolean} props.showActions If false, hide all action buttons
 * @param {string} props.apiUrl API URL for fetching documents
 * @param {string} props.deleteApiUrl API URL for deleting documents
 * @param {Function} props.onDocumentDeleted Callback when a document is deleted
 */
const DocumentsList = ({
  userId,
  readOnly = false,
  showActions = true,
  apiUrl = `${import.meta.env.VITE_APP_API_BASE_PROFILE_DOC_URL}/vendor-documents/user`,
  deleteApiUrl = `${import.meta.env.VITE_APP_API_BASE_PROFILE_DOC_URL}/vendor-documents`,
  onDocumentDeleted
}) => {
  const { t } = useTranslation();
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState({});
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);

  const fetchDocuments = React.useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(`${apiUrl}/${userId}`);
      setDocuments(response.data);
    } catch (err) {
      console.error('Error fetching documents:', err);
      setError(t('Failed to load documents'));
    } finally {
      setLoading(false);
    }
  }, [apiUrl, userId, t]);

  useEffect(() => {
    if (userId) {
      fetchDocuments();
    }
  }, [userId, fetchDocuments]);

  const handlePreview = (file) => {
    setPreviewFile({
      url: file.fileViewUri,
      downloadUrl: file.fileDownloadUri,
      name: file.originalFileName || file.fileName
    });
    setPreviewVisible(true);
  };

  const confirmDelete = (document) => {
    setDocumentToDelete(document);
    setDeleteModalVisible(true);
  };

  const handleDelete = async () => {
    if (!documentToDelete) return;

    try {
      await axios.delete(`${deleteApiUrl}/${documentToDelete.id}`);
      message.success(t('Document deleted successfully'));

      // Refresh the documents list instead of manually filtering
      fetchDocuments();

      // Call the callback if provided
      if (onDocumentDeleted) {
        onDocumentDeleted(documentToDelete);
      }
    } catch (err) {
      console.error('Error deleting document:', err);
      message.error(t('Failed to delete document'));
    } finally {
      setDeleteModalVisible(false);
      setDocumentToDelete(null);
    }
  };

  // Get appropriate icon based on file type
  const getFileIcon = (fileName) => {
    if (!fileName) return <FileOutlined />;

    const extension = fileName.split('.').pop().toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
      return <FileImageOutlined />;
    } else if (extension === 'pdf') {
      return <FilePdfOutlined />;
    } else if (['doc', 'docx', 'rtf', 'odt'].includes(extension)) {
      return <FileWordOutlined />;
    } else if (['xls', 'xlsx', 'csv', 'ods'].includes(extension)) {
      return <FileExcelOutlined />;
    } else if (['txt', 'text', 'json', 'xml', 'html', 'htm', 'md', 'log'].includes(extension)) {
      return <FileTextOutlined />;
    } else if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(extension)) {
      return <FileZipOutlined />;
    } else {
      return <FileUnknownOutlined />;
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes) return '';

    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="documents-list-loading">
        <Spin size="large" />
        <div className="loading-text">{t('Loading documents...')}</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="documents-list-error">
        <Text type="danger">{error}</Text>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <Empty
        description={t('No documents found')}
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  return (
    <div className="documents-list-container">
      <List
        className="documents-list"
        itemLayout="horizontal"
        dataSource={documents}
        renderItem={document => (
          <List.Item
            key={document.id}
            actions={showActions ? [
              <Tooltip title={t('View')}>
                <Button
                  icon={<EyeOutlined />}
                  onClick={() => handlePreview(document)}
                  size="small"
                />
              </Tooltip>,
              <Tooltip title={t('Download')}>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={() => window.open(document.fileDownloadUri, '_blank')}
                  size="small"
                />
              </Tooltip>,
              !readOnly && (
                <Tooltip title={t('Delete')}>
                  <Button
                    icon={<DeleteOutlined />}
                    onClick={() => confirmDelete(document)}
                    size="small"
                    danger
                  />
                </Tooltip>
              )
            ].filter(Boolean) : []}
          >
            <List.Item.Meta
              avatar={getFileIcon(document.originalFileName || document.fileName)}
              title={
                <a onClick={() => handlePreview(document)}>
                  {document.originalFileName || document.fileName}
                </a>
              }
              description={
                <Space direction="vertical" size={0}>
                  {document.description && (
                    <Text type="secondary">{document.description}</Text>
                  )}
                  <Space size="large">
                    {document.fileSize && (
                      <Text type="secondary">{formatFileSize(document.fileSize)}</Text>
                    )}
                    {document.uploadDate && (
                      <Text type="secondary">{formatDate(document.uploadDate)}</Text>
                    )}
                  </Space>
                </Space>
              }
            />
          </List.Item>
        )}
      />

      <FilePreviewModal
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
        fileUrl={previewFile.url}
        downloadUrl={previewFile.downloadUrl}
        fileName={previewFile.name}
      />

      <Modal
        title={t('Confirm Delete')}
        open={deleteModalVisible}
        onOk={handleDelete}
        onCancel={() => setDeleteModalVisible(false)}
        okText={t('Delete')}
        cancelText={t('Cancel')}
        okButtonProps={{ danger: true }}
      >
        <p>{t('Are you sure you want to delete this document?')}</p>
        <p><strong>{documentToDelete?.originalFileName || documentToDelete?.fileName}</strong></p>
        <p>{t('This action cannot be undone.')}</p>
      </Modal>
    </div>
  );
};

export default DocumentsList;
