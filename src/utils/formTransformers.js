import dayjs from 'dayjs';

export const transformProfileData = (data) => {
  const transformedData = {};
  console.log('Backend data to transform:', data);

  Object.keys(data).forEach(key => {
    if (key === 'id' || key === 'userId') {
      return;
    }

    // Keep the original camelCase field names for the form
    const formFieldName = key;

    if (key === 'profilePicture' || key === 'businessLogo' || key === 'certificate') {
      transformedData[formFieldName] = data[key];
    } else if (key === 'dateOfBirth' && data[key]) {
      transformedData[formFieldName] = dayjs(data[key]);
    } else if (key === 'receiveNotificationsFromVendors' || key === 'visibleToVendors' || key === 'profileComplete') {
      transformedData[formFieldName] = Boolean(data[key]);
    } else {
      transformedData[formFieldName] = data[key];
    }
  });

  console.log('Transformed data for form:', transformedData);
  return transformedData;
};

export const transformFormDataForBackend = (formData, isCustomer) => {
  const transformedData = {
    firstName: formData.firstName || '',
    lastName: formData.lastName || '',
    emailAddress: formData.emailAddress || '',
    phoneNumber: formData.phoneNumber || '',
    dateOfBirth: formData.dateOfBirth ? dayjs(formData.dateOfBirth).format('YYYY-MM-DD') : null,
    profilePicture: formData.profilePicture || null,
    primaryStreetAddress: formData.primaryStreetAddress || '',
    primaryCity: formData.primaryCity || '',
    primaryState: formData.primaryState || '',
    primaryZipCode: formData.primaryZipCode || '',
    country: formData.country || '',
    secondaryStreetAddress: formData.secondaryStreetAddress || '',
    secondaryCity: formData.secondaryCity || '',
    secondaryState: formData.secondaryState || '',
    secondaryZipCode: formData.secondaryZipCode || '',
    designationOrRole: formData.designationOrRole || '',
    industryOrDomain: formData.industryOrDomain || '',
    companyName: formData.companyName || '',
    preferredVendorType: formData.preferredVendorType || '',
    subcategoriesOfInterest: formData.subcategoriesOfInterest || [],
    categoriesOfServicesNeeded: formData.categoriesOfServicesNeeded || [],
    receiveNotificationsFromVendors: formData.receiveNotificationsFromVendors || false,
    visibleToVendors: formData.visibleToVendors || false,
    userId: formData.userId,
    profileComplete: formData.profileComplete || false
  };

  return transformedData;
};

const mapCustomerFields = (key, formData, transformedData) => {
  const fieldMappings = {
    'street_address': 'primaryStreetAddress',
    'city': 'primaryCity',
    'state': 'primaryState',
    'zip_code': 'primaryZipCode',
    'country': 'country',
    'email': 'emailAddress',
    'phone_number': 'phoneNumber',
    'date_of_birth': 'dateOfBirth',
    'receive_notifications_from_vendors': 'receiveNotificationsFromVendors',
    'visible_to_vendors': 'visibleToVendors',
    'designation/role': 'designationOrRole',
    'industry/domain': 'industryOrDomain'
  };

  if (fieldMappings[key]) {
    transformedData[fieldMappings[key]] = formData[key];
  } else {
    const backendFieldName = key.replace(/_([a-z])/g, (_, p1) => p1.toUpperCase());
    transformedData[backendFieldName] = formData[key];
  }
};

const mapVendorFields = (key, formData, transformedData) => {
  const fieldMappings = {
    'business_name': 'businessName',
    'business_email': 'businessEmail',
    'business_phone': 'businessPhone',
    'street_address': 'streetAddress',
    'city': 'city',
    'state': 'state',
    'zip_code': 'zipCode',
    'country': 'country',
    'business_category': 'businessCategory',
    'business_subcategory': 'businessSubcategory',
    'business_website': 'businessWebsite',
    'years_of_experience': 'yearsOfExperience',
    'product_name': 'productName',
    'product_description': 'productDescription',
    'product_category': 'productCategory',
    'registration_number': 'registrationNumber',
    'regions_supported': 'regionsSupported',
    'license_details': 'licenseDetails'
  };

  if (fieldMappings[key]) {
    transformedData[fieldMappings[key]] = formData[key];
  } else {
    const backendFieldName = key.replace(/_([a-z])/g, (_, p1) => p1.toUpperCase());
    transformedData[backendFieldName] = formData[key];
  }
};

export const validateRequiredFields = (data, isCustomer) => {
  if (isCustomer) {
    if (!data.firstName || !data.lastName || !data.emailAddress) {
      throw new Error('Missing required personal information fields');
    }
  } else {
    if (!data.businessName || !data.businessEmail) {
      throw new Error('Missing required business information fields');
    }
  }
}; 