package com.userprofile.user_profile_service.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.userprofile.user_profile_service.entity.CustomerDetails;
import com.userprofile.user_profile_service.entity.VendorDetails;
import com.userprofile.user_profile_service.repository.CustomerDetailsRepository;
import com.userprofile.user_profile_service.repository.VendorDetailsRepository;

import java.util.Optional;

/**
 * Service class for managing user profiles (both customer and vendor).
 */
@Service
public class ProfileService {

    @Autowired
    private CustomerDetailsRepository customerDetailsRepository;

    @Autowired
    private VendorDetailsRepository vendorDetailsRepository;

    /**
     * Get customer details by user ID
     *
     * @param userId The unique identifier for the user
     * @return Optional containing customer details if found
     */
    public Optional<CustomerDetails> getCustomerDetailsByUserId(String userId) {
        return customerDetailsRepository.findByUserId(userId);
    }

    /**
     * Get vendor details by user ID
     *
     * @param userId The unique identifier for the user
     * @return Optional containing vendor details if found
     */
    public Optional<VendorDetails> getVendorDetailsByUserId(String userId) {
        return vendorDetailsRepository.findByUserId(userId);
    }

    /**
     * Create or update customer profile
     *
     * @param customerDetails The customer details to save
     * @return The saved customer details
     */
    @Transactional
    public CustomerDetails saveCustomerProfile(CustomerDetails customerDetails) {
        // Check if profile already exists for this user
        Optional<CustomerDetails> existingProfile = getCustomerDetailsByUserId(customerDetails.getUserId());

        if (existingProfile.isPresent()) {
            // Update existing profile
            CustomerDetails existing = existingProfile.get();
            customerDetails.setId(existing.getId());
        }

        return customerDetailsRepository.save(customerDetails);
    }

    /**
     * Create or update vendor profile
     *
     * @param vendorDetails The vendor details to save
     * @return The saved vendor details
     */
    @Transactional
    public VendorDetails saveVendorProfile(VendorDetails vendorDetails) {
        // Check if profile already exists for this user
        Optional<VendorDetails> existingProfile = getVendorDetailsByUserId(vendorDetails.getUserId());

        if (existingProfile.isPresent()) {
            // Update existing profile
            VendorDetails existing = existingProfile.get();
            vendorDetails.setId(existing.getId());
        }

        return vendorDetailsRepository.save(vendorDetails);
    }

    /**
     * Check if a customer profile exists for the given user ID
     *
     * @param userId The user ID to check
     * @return true if a profile exists, false otherwise
     */
    public boolean customerProfileExists(String userId) {
        return getCustomerDetailsByUserId(userId).isPresent();
    }

    /**
     * Check if a vendor profile exists for the given user ID
     *
     * @param userId The user ID to check
     * @return true if a profile exists, false otherwise
     */
    public boolean vendorProfileExists(String userId) {
        return getVendorDetailsByUserId(userId).isPresent();
    }
}
