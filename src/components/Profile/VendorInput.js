export const VendorInputs = {
    business_basic_details: [
        {
            id: 1,
            label: "Business Name",
            type: "text",
        },
        {
            id: 2,
            label: "Upload Logo",
            type: "file",
            accept: "image/*",
            fileType: "logo",
            description: "Upload your business logo (JPG, PNG, max 5MB)"
        },
        {
            id: 3,
            label: "Business Email",
            type: "text",
        },
        {
            id: 4,
            label: "Business Phone",
            type: "text",
        },
    ],
    business_address: [
        {
            id: 1,
            label: "Street Address",
            type: "text",
        },
        {
            id: 2,
            label: "City",
            type: "text",
        },
        {
            id: 3,
            label: "State",
            type: "text",
        },
        {
            id: 4,
            label: "Zip Code",
            type: "text",
        },
        {
            id: 5,
            label: "Country",
            type: "text",
        }
    ],
    business_details: [
        {
            id: 1,
            label: "Business Category",
            type: "text",
        },
        {
            id: 2,
            label: "Business Subcategory",
            type: "text",
        },
        {
            id: 3,
            label: "Business Website",
            type: "text",
        },
        {
            id: 4,
            label: "Years of Experience",
            type: "text",
        },
        {
            id: 5,
            label: "Upload Certificate",
            type: "file",
            accept: ".pdf,.doc,.docx",
            fileType: "certificate",
            description: "Upload your business certificates (PDF, DOC, max 10MB)"
        },
    ],
    product_details: [
        {
            id: 1,
            label: "Product Name",
            type: "text",
        },
        {
            id: 2,
            label: "Product Description",
            type: "text",
        },
        {
            id: 3,
            label: "Product Category",
            type: "text",
        }
    ],
    preferences: [
        {
            id: 1,
            label: "Registration Number",
            type: "text",
        },
        {
            id: 2,
            label: "Regions Supported",
            type: "text",
        },
        {
            id: 3,
            label: "License Details",
            type: "text",
        }
    ],
    additional_documents: [
        {
            id: 1,
            label: "Upload Additional Documents",
            type: "multi-file",
            accept: ".pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx,.csv,.txt",
            fileType: "vendor_document",
            description: "Upload additional documents (PDF, DOC, JPG, XLS, etc., max 10MB per file)",
            multiple: true,
            maxCount: 10
        }
    ]
}